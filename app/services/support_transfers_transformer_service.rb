# frozen_string_literal: true

class SupportTransfersTransformerService < TransfersTransformerService
  def call
    @transfers.map do |transfer|
      collections = transform_types(transfer)
      collections[:errors] = []
      map_error_messages(transfer, collections[:errors])

      log_line = {
        id: transfer[:shopify_order_id],
        url: "/orders/#{transfer[:shopify_order_id]}",
        order_id: transfer[:shopify_order_names],
        date: transfer[:shopify_created_at],
        invoices: collections[:invoices],
        credit_notes: collections[:credit_notes],
        transactions: collections[:transactions],
        errors: collections[:errors]
      }

      if (index = transfer[:target_types].index("Transaction Assignment Hint"))
        log_line[:tah] = { last_action: transfer[:last_action][index] }
      end

      log_line
    end
  end

  private

  def map_error_messages(transfer, errors)
    transfer[:error_messages]&.each_with_index do |error, index|
      next if error.blank?

      error_data = {
        title: error,
        doc_type: transfer[:target_types].try("[]", index),
        error_id: transfer[:error_ids].try("[]", index),
        helpscout_id: transfer[:error_helpscout_id].try("[]", index),
        error_type: transfer[:error_type].try("[]", index) || transfer[:shopifyr_type].try("[]", index),
        error_info_internal: transfer[:error_info_internal] || "Error hat keine interne Info",
        shopify_type: transfer[:type].try("[]", index)
      }

      errors << error_data
    end
  end
end
