# frozen_string_literal: true

module Lexoffice
  # Create a lexoffice transaction assignment hint (TAH)
  class TahCreationService < ApplicationService
    def initialize(shop_id, invoice_info_target_id, authorization_key, order_id, order_name)
      @shop_id = shop_id
      @voucher_id = invoice_info_target_id
      @external_reference = authorization_key
      @order_id = order_id
      @order_name = order_name
    end

    def call
      @shop = Shop.find(@shop_id)
      sync_info = create_sync_info
      response = Lexoffice::TransactionAssignmentHint.new(@shop.lexoffice_token, @voucher_id,
                                                          @external_reference).create
      tah_target_id = response['voucherId']
      sync_info.update!(last_action: 'Created', target_id: tah_target_id)
      response
    end

    private

    def create_sync_info
      SyncInfo.create(shop_id: @shop.id,
                      shopify_id: @order_id,
                      shopify_order_id: @order_id,
                      shopify_order_name: @order_name,
                      target_type: 'Transaction Assignment Hint',
                      last_action: 'Job Started')
    end
  end
end
