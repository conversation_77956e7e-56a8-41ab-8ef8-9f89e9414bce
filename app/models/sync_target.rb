# frozen_string_literal: true

require 'uri'
require 'net/http'

class SyncTarget
  def initialize(shop, is_import_job = false)
    @shop = shop
    @settings = shop.shop_setting
    @is_import_job = is_import_job
    @send_mail = @settings.send_invoice_mail && !@is_import_job
    @mark_due_immediately = @settings.mark_due_immediately
    @shop.refresh_token_if_expired
    Rails.error.set_context(token_expires: Time.zone.at(@shop.lexoffice_token_expires_at).to_datetime)
  end

  def handle_refund(refund)
    return unless @shop.lexoffice_token.present? && refund.has_successful_transaction?

    auth_retries ||= 0
    order = @shop.with_shopify_session { ShopifyAPI::Order.find(id: refund.order_id) }
    tax_type = Lexoffice::TaxTypesService.call(@shop, order)
    contact = CreateOrGetContactService.new(order, @shop, tax_type).call
    @send_mail = @settings.send_invoice_mail && !@is_import_job && !order.mailing_excluded?(@shop)

    # if no contact gets passed into CreditNoteDataBuilderService, CPD Customer will be used
    credit_note, sync_info = CreditNoteDataBuilderService.new.call(@shop, refund, @is_import_job, order, @settings,
                                                                   contact.try(:[], 'id'), contact,
                                                                   order.taxes_included, tax_type)
    original_invoice_id = credit_note&.get_original_invoice(order)

    return if credit_note.nil?

    # Create invoice
    CreditNoteCreationService.new(@mark_due_immediately, original_invoice_id, credit_note, sync_info).call
    # Queue Mail Job if invoice was created successfully
    SendMailJob.perform_async(sync_info.id) if @send_mail && sync_info.doc_created?
    # Queue transaction assignment hint creation if setting enabled
    if @settings.enable_tah_creation
      TahCreationJob.perform_async(@shop.id,
                                   order.id,
                                   order.name,
                                   sync_info.target_id,
                                   { kind: 'refund', transaction_id: refund.successful_refund_transaction&.id })
    end

    credit_note.id
  rescue RestClient::Unauthorized => e
    @shop.refresh_token_if_expired(true)
    @shop.reload
    retry if (auth_retries += 1) < 2
    raise(e)
  end

  def create_order(order, rule)
    return if @shop.lexoffice_token.blank?

    @send_mail = @settings.send_invoice_mail && !@is_import_job && !order.mailing_excluded?(@shop)
    @use_brutto = order.taxes_included
    auth_retries ||= 0
    tax_type = Lexoffice::TaxTypesService.call(@shop, order)
    contact = CreateOrGetContactService.new(order, @shop, tax_type).call
    invoice, sync_info = InvoiceDataBuilderService.new.call(@shop, order, rule, @is_import_job, order.taxes_included,
                                                            @settings, tax_type, contact.try(:[], 'id'), contact)
    return if invoice.nil?

    InvoiceCreationService.new(invoice, @mark_due_immediately, sync_info, @send_mail, order, @settings).call
    # Queue Mail Job if invoice was created successfully
    if @send_mail &&
       sync_info.doc_created? &&
       order.valid_email?
      MailJobQueuingService.call(@settings, sync_info)
    end
    # Queue transaction assignment hint creation if setting enabled
    TahCreationJob.perform_async(@shop.id, order.id, order.name, sync_info.target_id) if @settings.enable_tah_creation

    invoice.id
  rescue RestClient::Unauthorized => e
    @shop.refresh_token_if_expired(true)
    @shop.reload
    retry if (auth_retries += 1) < 2
    raise(e)
  end
end
