# frozen_string_literal: true

module Api
  class AuthenticatedController < ApplicationController
    include ShopifyApp::EnsureHasSession
    before_action :handle_access_scopes
    before_action :check_app_was_reinstalled

    # Set the current shop for the billing gem
    def current_shop
      @current_shop ||= find_shop_from_session
    end

    private

    def find_or_create_shop_from_session
      return if current_shopify_session.blank?

      @current_shop = Shop.find_or_create_new(current_shopify_session)
      @current_shop.update_shop_info
    end

    def find_shop_from_session
      return if current_shopify_session.blank?

      @current_shop = Shop.find_by(shopify_domain: current_shopify_session.shop)
    end

    def handle_access_scopes
      return if current_shop.blank? || current_shop&.access_scopes.present?

      current_shop.add_or_update_access_scopes
    end

    # Check if the shop reinstalled the app. If so, reset the uninstalled_at timestamp to nil
    def check_app_was_reinstalled
      return if current_shop.blank? || current_shop&.uninstalled_at.blank?

      current_shop.update!(uninstalled_at: nil)
    end
  end
end
