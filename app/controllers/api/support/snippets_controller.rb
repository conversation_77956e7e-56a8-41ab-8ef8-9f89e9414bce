# frozen_string_literal: true

module Api
  module Support
    class SnippetsController < SupportAuthenticatedController
      before_action :set_snippet, only: %i[update destroy]

      # GET /snippets or /snippets.json
      def index
        @snippets = Snippet.all.paginate(page: snippet_params[:page]).order(id: :desc).per_page(5)
        render json: { snippets: @snippets, total_pages: @snippets.total_pages }
      end

      # POST /snippets or /snippets.json
      def create
        @snippet = Snippet.new(name: snippet_params[:name], code: snippet_params[:code],
          description: snippet_params[:description])

        if @snippet.save
          render json: { status: :ok }
        else
          render json: { status: :unprocessable_entity }
        end
      end

      # PATCH/PUT /snippets/1 or /snippets/1.json
      def update
        if @snippet.update(snippet_params)
          render json: { status: :ok }
        else

          render json: { status: :unprocessable_entity }
        end
      end

      # DELETE /snippets/1 or /snippets/1.json
      def destroy
        @snippet.destroy

        render json: { status: :ok }
      end

      def preview
        @errors = ""
        @current_shop = Shop.find_by(shopify_domain: params[:shop_domain])
        text = params[:code]
        order_id = params[:order_id]
        Rails.logger.info "Support user #{current_support_user&.email} previewed liquid code for shop #{params[:shop_domain]}, order #{order_id}"
        if text.present? && @current_shop.present? && order_id.present?
          @preview, @errors, @liquid_error = LiquidCodeService.call(@current_shop, @errors, text, order_id)
        else
          @errors = t("support.snippets.error")
        end
        render json: { preview: @preview, errors: @errors, liquid_error: @liquid_error }
      end

      private

      # Use callbacks to share common setup or constraints between actions.
      def set_snippet
        @snippet = Snippet.find(params[:id])
      end

      # Only allow a list of trusted parameters through.
      def snippet_params
        params.permit(:name, :code, :description, :page, :shop_domain, :order_id)
      end
    end
  end
end
