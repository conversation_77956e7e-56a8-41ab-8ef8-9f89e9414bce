# frozen_string_literal: true

module Api
  class DocumentsController < AuthenticatedController
    before_action :find_or_create_shop_from_session

    def redirect_to_invoice
      order_id = params[:id]
      @sync_info = SyncInfo.for_shop(@current_shop.id).find_by(shopify_id: order_id)
      if @sync_info.nil? || @sync_info.target_id.nil? || @current_shop.lexoffice_token.blank?
        render json: { status: "not found" }
        return
      end

      @invoice_url = "#{ENV.fetch("LEXOFFICE_SITE", nil)}/vouchers#!/VoucherView/Invoice/#{@sync_info.target_id}"
      render json: { url: @invoice_url }
    end

    def invoice_pdf
      order_id = params[:id]
      @sync_info = SyncInfo.for_shop(@current_shop.id).find_by(shopify_id: order_id)
      if @sync_info.nil? || @sync_info.target_id.nil? || @current_shop.lexoffice_token.blank?
        render json: { status: "not found" }
        return
      end

      respond_to do |format|
        format.html do
          render_pdf
        end
        format.pdf do
          render_pdf
        end
      end
    end

    private

    def render_pdf
      invoice = Lexoffice::RenderInvoicePdfService.call(@sync_info)

      send_data(invoice[:binary], filename: invoice[:filename], type: "application/pdf")
    end
  end
end
