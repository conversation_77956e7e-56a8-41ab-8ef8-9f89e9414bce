# frozen_string_literal: true

namespace :maintenance do
  namespace :db_migrations do
    # NOTE: This task is only needed after migration 20230417134616_set_shop_settings_defaults to replace nil values
    # with empty strings in invoice_posttext, refund_pretext, refund_posttext, credit_note_mail_body and
    # credit_note_mail_subject.
    desc 'For each shop_settings replace nil values with empty strings'
    task replace_nil: :environment do
      ShopSetting.transaction do
        # rubocop:disable Rails/SkipsModelValidations
        ShopSetting.where(invoice_posttext: nil).update_all(invoice_posttext: '')
        ShopSetting.where(refund_pretext: nil).update_all(refund_pretext: '')
        ShopSetting.where(refund_posttext: nil).update_all(refund_posttext: '')
        ShopSetting.where(credit_note_mail_body: nil).update_all(credit_note_mail_body: '')
        ShopSetting.where(credit_note_mail_subject: nil).update_all(credit_note_mail_subject: '')
        # rubocop:enable Rails/SkipsModelValidations
      end
    end

    # rubocop:disable Rails/SkipsModelValidations
    desc 'Copy shopify_order_id values to new_shopify_order_id in batches'
    task copy_order_ids: :environment do
      batch_size = 50_000
      total_records = SyncInfo.where(new_shopify_order_id: nil).count
      total_batches = (total_records / batch_size.to_f).ceil
      processed = 0

      puts "Starting to process #{total_records} records in #{total_batches} batches"

      # Process in batches directly using batch_size
      SyncInfo.where(new_shopify_order_id: nil)
              .in_batches(of: batch_size) do |batch|
        # Update all records in the current batch at once
        updated_count = batch.update_all('new_shopify_order_id = shopify_order_id::bigint')

        processed += updated_count
        puts "Processed #{processed}/#{total_records} records (#{(processed.to_f / total_records * 100).round(2)}%)"
      end

      puts "Completed! #{processed} records updated."
    end

    desc 'Copy order_id values to new_order_id in error_logs table in batches'
    task copy_error_log_order_ids: :environment do
      batch_size = 50_000
      total_records = ErrorLog.where(new_order_id: nil).count
      total_batches = (total_records / batch_size.to_f).ceil
      processed = 0

      puts "Starting to process #{total_records} records in #{total_batches} batches"

      # Process in batches directly using batch_size
      ErrorLog.where(new_order_id: nil)
              .in_batches(of: batch_size) do |batch|
        # Update all records in the current batch at once
        updated_count = batch.update_all('new_order_id = order_id::bigint')

        processed += updated_count
        puts "Processed #{processed}/#{total_records} records (#{(processed.to_f / total_records * 100).round(2)}%)"
      end

      puts "Completed! #{processed} records updated."
    end
    # rubocop:enable Rails/SkipsModelValidations

    desc "Set uninstalled_at timestamp for all shops where the app is uninstalled or shop is not found"
    task mark_as_uninstalled: :environment do
      Shop.find_each do |shop|
        AppInstalledCheckJob.perform_async({ shop_id: shop.id })
      end
    end
  end
end
