# frozen_string_literal: true

Rails.application.routes.draw do
  mount CrossPromotionApp::Engine, at: "/cross_promotion_app"
  mount ShopifyBilling::Engine, at: "/shopify_billing"
  root to: "home#index"

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check

  # TODO: Temporary fix before shopify_api upgrade
  get "/login", to: "shopify_app/sessions#new"
  post "/login", to: "shopify_app/sessions#create"

  # CALLBACKS
  get "/auth/shopify/callback", to: "shopify_app/callback#callback"
  get "/auth/lexoffice/callback", to: "omniauth_callbacks#lexoffice"
  get "handle_charge", to: redirect("/shopify_billing/handle_charge")

  # Support UI routes - moved before Shopify app engine
  get "/support", to: "support#index"
  get "api/support", to: redirect("/support")
  match "/support/*path", to: "support#index", via: %i[get post], as: :support_root

  mount ShopifyApp::Engine, at: "/api"
  get "/api", to: redirect(path: "/") # Needed because our engine root is /api but that breaks FE routing

  namespace :api do
    get "import/running", to: "import#import_running"
    post "import/start", to: "import#start"
    post "import/count", to: "import#count"

    get "shop_status", to: "shop_status#status"
    post "disconnect_lexoffice", to: "shop_status#disconnect_lexoffice"

    get "shop_settings", to: "shop_settings#shop_settings"
    post "shop_settings", to: "shop_settings#update"

    get "transfer_history", to: "transfer_history#transfer_history"
    post "retry_transfers", to: "transfer_history#retry_transfers"
    post "retry_skipped_transfers", to: "transfer_history#retry_skipped_transfers"

    post "liquid_preview", to: "shop_settings#liquid_preview"
    get "sample_order", to: "sample_data#sample_order"

    get "tags", to: "shop_settings#tags"

    get "help/default_articles", to: "help_scout#default_articles"
    get "help/search/:query", to: "help_scout#search_articles"
    get "help/article/:id", to: "help_scout#fetch_article"
    get "beacon_messages", to: "beacon_messages#index"

    get "redirect_to_invoice", to: "documents#redirect_to_invoice"
    get "get_invoice_pdf", to: "documents#invoice_pdf"
    get "ui-extensions/invoice/:order_id", to: "ui_extensions/customer#show_invoice"

    get "order_tags", to: "services#order_tags"

    namespace :support do
      root to: "support#index", as: :support_user_root
      resources :beacon_messages
      resources :coupon_codes
      resources :snippets

      get "merchant", to: "support#view_merchant"
      get "support_user", to: "support#support_user"
      get "autocomplete", to: "support#autocomplete"
      get "transfer_history", to: "support#transfer_history"
      post "retry_transfers", to: "support#retry_transfers"
      get "running", to: "support#import_running"
      post "start", to: "support#start"
      post "count", to: "support#count"
      post "activate_plan", to: "support#activate_plan"
      post "toggle_test_shop", to: "support#toggle_test_shop"
      get "order", to: "support#order"
      get "login", to: "support_authenticated#login"
      post "apply_discount", to: "support#apply_discount"
      post "delete_rows", to: "support#delete_entities"

      controller :snippets do
        post "/snippets", to: "snippets#create"
        post "/snippets/preview", to: "snippets#preview"
        post "/snippets/show_snippets_table", to: "snippets#show_snippets_table", as: "show_snippets_table"
      end
    end
  end

  controller :lexoffice_webhooks do
    post "lexoffice_token_revoked" => :lexoffice_token_revoked
    post "invoice_status_changed" => :invoice_status_changed
    post "credit_note_status_changed" => :credit_note_status_changed
  end

  controller :webhooks do
    post "uninstall" => :uninstall
    post "customer_redact" => :customer_redact
    post "customer_data_request" => :customer_data_request
    post "shop_redact" => :shop_redact

    post "sync/orders_create" => :orders_create
    post "sync/orders_fulfilled" => :orders_fulfilled
    post "sync/orders_paid" => :orders_paid
    post "sync/refunds_create" => :refunds_create
    post "sync/tender_transactions_create" => :tender_transactions_create
  end

  controller :docs do
    get "/docs/:order_id/:token" => :download_pdf, as: "download_pdf"
  end

  # SUPPORT BACKEND
  devise_for :support_users
  devise_scope :support_user do
    match "/support_users/auth/google_oauth2",
      via: %i[get post],
      to: "support_users/omniauth_callbacks#passthru",
      as: :support_user_google_oauth2_omniauth_authorize

    match "/support_users/auth/google_oauth2/callback",
      via: %i[get post],
      to: "support_users/omniauth_callbacks#google_oauth2",
      as: :support_user_google_oauth2_omniauth_callback
  end

  namespace :support do
    resources :beacon_messages

    controller :support do
      get "/" => :index, as: :support_user_root
      get "/shops" => :autocomplete
      get "/view_order" => :view_order
      get "/merchant/:shop" => :view_merchant
      post "/activate_import" => :activate_import
      post "/retry_all" => :retry_all
      post "/reset" => :reset
      post "/toggle_internal_test_shop" => :toggle_internal_test_shop
      post "/count_orders" => :support_count_orders
      post "/start_import" => :support_start_import
      post "/reset_plan" => :reset_plan
      post "/requeue_entity" => :requeue_entity, as: "support_retry"
      get "/show_errors_table" => :show_errors_table, as: "show_errors_table"
      post "/show_sync_infos" => :show_sync_infos
      get "/show_sync_infos" => :show_sync_infos # for pagination
      post "/show_imports_history" => :show_imports_history
      get "/show_imports_history" => :show_imports_history # for pagination
      post "/set_plan_discount" => :set_plan_discount
      post "/set_import_discount" => :set_import_discount
      post "/get_entity" => :entity
      post "/retry_one" => :retry_one
      post "/delete_data" => :delete_data
    end

    resources :coupon_codes, only: %i[index create destroy]
    controller :coupon_codes do
      post "/coupon_codes/show_table" => :show_table
      get "/coupon_codes/show_table" => :show_table # for pagination
    end

    resources :snippets
    controller :snippets do
      post "/snippets/preview" => :preview
      post "/snippets/show_snippets_table" => :show_snippets_table, as: "show_snippets_table"
    end
  end

  namespace :support_users do
    controller :omniauth_callbacks do
      post "/support_users", to: "omniauth_callbacks#google_oauth2"
    end
  end

  # SIDEKIQ TOOLING
  require "sidekiq/web"
  Sidekiq::Web.use Rack::Auth::Basic do |username, password|
    ActiveSupport::SecurityUtils.secure_compare(Digest::SHA256.hexdigest(username),
      Digest::SHA256.hexdigest(ENV.fetch("SIDEKIQ_USERNAME", nil))) &
      ActiveSupport::SecurityUtils.secure_compare(Digest::SHA256.hexdigest(password),
        Digest::SHA256.hexdigest(ENV.fetch("SIDEKIQ_PASSWORD", nil)))
  end
  mount Sidekiq::Web, at: "/sidekiq"

  # Any other routes will just render the react app
  match "*path", via: %i[get post], to: "home#index", constraints: lambda { |req|
    req.path.exclude? "rails/active_storage"
  }
end
