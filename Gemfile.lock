GIT
  remote: https://github.com/eshopguide/centralized-logging.git
  revision: 9e1d82af21df517a6970f03aa247eb4441e3fe44
  specs:
    central_event_logger (0.1.7)
      pg (~> 1.1)
      rails (~> 7.0)
      tzinfo-data (~> 1.2024.2)

GIT
  remote: https://github.com/eshopguide/cross-promotion-app.git
  revision: 504f8c5641507d3bc7184def51f897b3e32a0baa
  tag: v0.3.0
  specs:
    cross_promotion_app (0.2.1)
      activestorage (~> 7.0)
      pg (~> 1.1)
      rails (~> 7.0)
      sprockets-rails (~> 3.4)
      turbo-rails (~> 1.1)

GIT
  remote: https://github.com/eshopguide/shopify_billing.git
  revision: 8e6c8cf4c59ba7fd7c3806df5ac4bafb731780f9
  specs:
    shopify_billing (1.1.0)
      rails (~> 7.0)
      shopify_api (~> 14.4)
      shopify_app (>= 22.3)

GEM
  remote: https://rubygems.org/
  specs:
    MailchimpTransactional (1.0.50)
      excon (>= 0.76.0, < 1)
      json (~> 2.1, >= 2.1.0)
    actioncable (7.1.5)
      actionpack (= 7.1.5)
      activesupport (= 7.1.5)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (7.1.5)
      actionpack (= 7.1.5)
      activejob (= 7.1.5)
      activerecord (= 7.1.5)
      activestorage (= 7.1.5)
      activesupport (= 7.1.5)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (7.1.5)
      actionpack (= 7.1.5)
      actionview (= 7.1.5)
      activejob (= 7.1.5)
      activesupport (= 7.1.5)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (7.1.5)
      actionview (= 7.1.5)
      activesupport (= 7.1.5)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (7.1.5)
      actionpack (= 7.1.5)
      activerecord (= 7.1.5)
      activestorage (= 7.1.5)
      activesupport (= 7.1.5)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.1.5)
      activesupport (= 7.1.5)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (7.1.5)
      activesupport (= 7.1.5)
      globalid (>= 0.3.6)
    activemodel (7.1.5)
      activesupport (= 7.1.5)
    activemodel-serializers-xml (1.0.3)
      activemodel (>= 5.0.0.a)
      activesupport (>= 5.0.0.a)
      builder (~> 3.1)
    activerecord (7.1.5)
      activemodel (= 7.1.5)
      activesupport (= 7.1.5)
      timeout (>= 0.4.0)
    activerecord-session_store (2.0.0)
      actionpack (>= 5.2.4.1)
      activerecord (>= 5.2.4.1)
      multi_json (~> 1.11, >= 1.11.2)
      rack (>= 2.0.8, < 3)
      railties (>= 5.2.4.1)
    activeresource (6.1.4)
      activemodel (>= 6.0)
      activemodel-serializers-xml (~> 1.0)
      activesupport (>= 6.0)
    activestorage (7.1.5)
      actionpack (= 7.1.5)
      activejob (= 7.1.5)
      activerecord (= 7.1.5)
      activesupport (= 7.1.5)
      marcel (~> 1.0)
    activesupport (7.1.5)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      mutex_m
      securerandom (>= 0.3)
      tzinfo (~> 2.0)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    ast (2.4.2)
    audited (5.8.0)
      activerecord (>= 5.2, < 8.2)
      activesupport (>= 5.2, < 8.2)
    autoprefixer-rails (*********)
      execjs (~> 2)
    backports (3.24.1)
    bar-of-progress (0.1.3)
    base64 (0.2.0)
    bcrypt (3.1.18)
    benchmark (0.4.0)
    bigdecimal (3.1.9)
    bindex (0.8.1)
    blocks (2.8.0)
      call_with_params (~> 0.0.2)
      hashie
      rails (>= 3.0.0)
    bootstrap-datepicker-rails (********)
      railties (>= 3.0)
    bootstrap-editable-rails (0.0.9)
      railties (>= 3.1)
      sass-rails
    bootstrap-material-design (0.2.2)
      bootstrap-sass (~> 3.0)
    bootstrap-sass (3.4.1)
      autoprefixer-rails (>= 5.2.1)
      sassc (>= 2.0.0)
    bootstrap_form (2.7.0)
    brakeman (6.0.0)
    builder (3.3.0)
    byebug (11.1.3)
    call_with_params (0.0.2)
      activesupport (>= 3.0.0)
    capybara (3.39.1)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.8)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    code_analyzer (0.5.5)
      sexp_processor
    coderay (1.1.3)
    coffee-rails (5.0.0)
      coffee-script (>= 2.2.0)
      railties (>= 5.2.0)
    coffee-script (2.4.1)
      coffee-script-source
      execjs
    coffee-script-source (1.12.2)
    colorize (0.8.1)
    commonjs (0.2.7)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    countries (5.4.0)
      unaccent (~> 0.3)
    crack (0.4.5)
      rexml
    crass (1.0.6)
    csv (3.3.4)
    database_cleaner-active_record (2.1.0)
      activerecord (>= 5.a)
      database_cleaner-core (~> 2.0.0)
    database_cleaner-core (2.0.1)
    date (3.4.1)
    db_fixtures_dump (0.1.1)
    debase (0.2.9)
      debase-ruby_core_source (>= 3.4.1)
    debase-ruby_core_source (3.4.1)
    declarative (0.0.20)
    devise (4.9.2)
      bcrypt (~> 3.0)
      orm_adapter (~> 0.1)
      railties (>= 4.1.0)
      responders
      warden (~> 1.2.3)
    diff-lcs (1.6.1)
    digest-crc (0.6.4)
      rake (>= 12.0.0, < 14.0.0)
    docile (1.4.0)
    domain_name (0.5.20190701)
      unf (>= 0.0.5, < 1.0.0)
    dotenv (2.8.1)
    dotenv-rails (2.8.1)
      dotenv (= 2.8.1)
      railties (>= 3.2)
    drb (2.2.1)
    dry-cli (1.1.0)
    erubi (1.13.1)
    erubis (2.7.0)
    excon (0.99.0)
    execjs (2.8.1)
    factory_bot (6.2.1)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.2.0)
      factory_bot (~> 6.2.0)
      railties (>= 5.0.0)
    faker (3.2.0)
      i18n (>= 1.8.11, < 2)
    faraday (1.10.3)
      faraday-em_http (~> 1.0)
      faraday-em_synchrony (~> 1.0)
      faraday-excon (~> 1.1)
      faraday-httpclient (~> 1.0)
      faraday-multipart (~> 1.0)
      faraday-net_http (~> 1.0)
      faraday-net_http_persistent (~> 1.0)
      faraday-patron (~> 1.0)
      faraday-rack (~> 1.0)
      faraday-retry (~> 1.0)
      ruby2_keywords (>= 0.0.4)
    faraday-em_http (1.0.0)
    faraday-em_synchrony (1.0.0)
    faraday-excon (1.1.0)
    faraday-httpclient (1.0.1)
    faraday-multipart (1.0.4)
      multipart-post (~> 2)
    faraday-net_http (1.0.1)
    faraday-net_http_persistent (1.2.0)
    faraday-patron (1.0.0)
    faraday-rack (1.0.0)
    faraday-retry (1.0.3)
    fasterer (0.10.1)
      colorize (~> 0.7)
      ruby_parser (>= 3.19.1)
    ffi (1.17.2-arm64-darwin)
    ffi (1.17.2-x64-mingw-ucrt)
    ffi (1.17.2-x86-mingw32)
    ffi (1.17.2-x86_64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    font-awesome-rails (*******)
      railties (>= 3.2, < 8.0)
    gibbon (3.4.4)
      faraday (>= 1.0)
      multi_json (>= 1.11.0)
    gitlab (4.20.1)
      httparty (~> 0.20)
      terminal-table (>= 1.5.1)
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-apis-core (0.11.0)
      addressable (~> 2.5, >= 2.5.1)
      googleauth (>= 0.16.2, < 2.a)
      httpclient (>= 2.8.1, < 3.a)
      mini_mime (~> 1.0)
      representable (~> 3.0)
      retriable (>= 2.0, < 4.a)
      rexml
      webrick
    google-apis-iamcredentials_v1 (0.17.0)
      google-apis-core (>= 0.11.0, < 2.a)
    google-apis-storage_v1 (0.19.0)
      google-apis-core (>= 0.9.0, < 2.a)
    google-cloud-core (1.6.0)
      google-cloud-env (~> 1.0)
      google-cloud-errors (~> 1.0)
    google-cloud-env (1.6.0)
      faraday (>= 0.17.3, < 3.0)
    google-cloud-errors (1.3.1)
    google-cloud-storage (1.44.0)
      addressable (~> 2.8)
      digest-crc (~> 0.4)
      google-apis-iamcredentials_v1 (~> 0.1)
      google-apis-storage_v1 (~> 0.19.0)
      google-cloud-core (~> 1.6)
      googleauth (>= 0.16.2, < 2.a)
      mini_mime (~> 1.0)
    googleauth (1.5.2)
      faraday (>= 0.17.3, < 3.a)
      jwt (>= 1.4, < 3.0)
      memoist (~> 0.16)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    hash_diff (1.1.1)
    hashdiff (1.0.1)
    hashie (5.0.0)
    honeybadger (5.27.1)
      logger
      ostruct
    http-accept (1.7.0)
    http-cookie (1.0.5)
      domain_name (~> 0.5)
    httparty (0.23.1)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    httpclient (2.8.3)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    interactor (3.1.2)
    io-console (0.8.0)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jquery-rails (4.5.1)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.6.3)
    jwt (2.10.1)
      base64
    language_server-protocol (********)
    launchy (2.5.2)
      addressable (~> 2.8)
    less (2.6.0)
      commonjs (~> 0.2.7)
    less-rails (4.0.0)
      actionpack (>= 4)
      less (~> 2.6.0)
      sprockets (>= 2)
    lint_roller (1.1.0)
    liquid (5.4.0)
    liquid-validator (1.0.1)
      liquid (>= 2.4.0)
    logger (1.7.0)
    lograge (0.12.0)
      actionpack (>= 4)
      activesupport (>= 4)
      railties (>= 4)
      request_store (~> 1.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    memoist (0.16.2)
    method_source (1.1.0)
    migration_data (0.6.0)
    mime-types (3.4.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2023.0218.1)
    mini_magick (4.12.0)
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    mock_redis (0.36.0)
      ruby2_keywords
    multi_json (1.15.0)
    multi_xml (0.7.1)
      bigdecimal (~> 3.1)
    multipart-post (2.3.0)
    mutex_m (0.3.0)
    net-imap (0.5.7)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    netrc (0.11.0)
    nio4r (2.7.4)
    nokogiri (1.18.8)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.8-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x64-mingw-ucrt)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.8-x86_64-linux-gnu)
      racc (~> 1.4)
    oauth2 (1.4.7)
      faraday (>= 0.8, < 2.0)
      jwt (>= 1.0, < 3.0)
      multi_json (~> 1.3)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 3)
    octokit (9.2.0)
      faraday (>= 1, < 3)
      sawyer (~> 0.9)
    oj (3.16.10)
      bigdecimal (>= 3.0)
      ostruct (>= 0.2)
    omniauth (2.1.1)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-google-oauth2 (1.0.1)
      jwt (>= 2.0)
      oauth2 (~> 1.1)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.7.1)
    omniauth-lexoffice (0.1.5)
    omniauth-oauth2 (1.7.3)
      oauth2 (>= 1.4, < 3)
      omniauth (>= 1.9, < 3)
    omniauth-rails_csrf_protection (1.0.1)
      actionpack (>= 4.2)
      omniauth (~> 2.0)
    openssl (3.3.0)
    orm_adapter (0.5.0)
    os (1.1.4)
    ostruct (0.6.1)
    parallel (1.27.0)
    parallel_tests (4.2.1)
      parallel
    parser (3.3.8.0)
      ast (~> 2.4.1)
      racc
    pg (1.5.4)
    pg (1.5.4-x64-mingw-ucrt)
    pg (1.5.4-x86-mingw32)
    pp (0.6.2)
      prettyprint
    prettyprint (0.2.0)
    prism (1.4.0)
    pronto (0.11.3)
      gitlab (>= 4.4.0, < 5.0)
      httparty (>= 0.13.7, < 1.0)
      octokit (>= 4.7.0, < 10.0)
      rainbow (>= 2.2, < 4.0)
      rexml (>= 3.2.5, < 4.0)
      rugged (>= 0.23.0, < 2.0)
      thor (>= 0.20.3, < 2.0)
    pronto-brakeman (0.11.2)
      brakeman (>= 3.2.0)
      pronto (~> 0.11.0)
    pronto-eslint_npm (0.11.0)
      pronto (~> 0.11.0)
    pronto-rails_best_practices (0.11.0)
      pronto (~> 0.11.0)
      rails_best_practices (~> 1.16, >= 1.15.0)
    pronto-rails_schema (0.11.0)
      pronto (>= 0.10, < 0.12)
    pronto-rubocop (0.11.6)
      pronto (~> 0.11.0)
      rubocop (>= 0.63.1, < 2.0)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-nav (1.0.0)
      pry (>= 0.9.10, < 0.15)
    pry-rails (0.3.9)
      pry (>= 0.10.4)
    psych (3.3.4)
    public_suffix (6.0.1)
    puma (6.3.0)
      nio4r (~> 2.0)
    racc (1.8.1)
    rack (2.2.14)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-protection (2.2.4)
      rack
    rack-proxy (0.7.7)
      rack
    rack-session (1.0.2)
      rack (< 3)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (1.0.1)
      rack (< 3)
      webrick
    rails (7.1.5)
      actioncable (= 7.1.5)
      actionmailbox (= 7.1.5)
      actionmailer (= 7.1.5)
      actionpack (= 7.1.5)
      actiontext (= 7.1.5)
      actionview (= 7.1.5)
      activejob (= 7.1.5)
      activemodel (= 7.1.5)
      activerecord (= 7.1.5)
      activestorage (= 7.1.5)
      activesupport (= 7.1.5)
      bundler (>= 1.15.0)
      railties (= 7.1.5)
    rails-controller-testing (1.0.5)
      actionpack (>= 5.0.1.rc1)
      actionview (>= 5.0.1.rc1)
      activesupport (>= 5.0.1.rc1)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-i18n (7.0.7)
      i18n (>= 0.7, < 2)
      railties (>= 6.0.0, < 8)
    rails_12factor (0.0.3)
      rails_serve_static_assets
      rails_stdout_logging
    rails_autoscale_agent (0.12.0)
    rails_best_practices (1.23.2)
      activesupport
      code_analyzer (~> 0.5.5)
      erubis
      i18n
      json
      require_all (~> 3.0)
      ruby-progressbar
    rails_layout (1.0.42)
    rails_serve_static_assets (0.0.5)
    rails_stdout_logging (0.0.5)
    railties (7.1.5)
      actionpack (= 7.1.5)
      activesupport (= 7.1.5)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (2.2.2)
      rake
    rake (13.2.1)
    rdoc (6.3.4.1)
    redirect_safely (1.0.0)
      activemodel
    redis (4.7.1)
    redis-client (0.22.2)
      connection_pool
    redlock (2.0.6)
      redis-client (>= 0.14.1, < 1.0.0)
    regexp_parser (2.10.0)
    reline (0.6.1)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    request_store (1.5.1)
      rack (>= 1.4)
    require_all (3.0.0)
    responders (3.1.0)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rest-client (2.1.0-x86-mingw32)
      ffi (~> 1.9)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    retriable (3.1.2)
    rexml (3.4.1)
    rspec-core (3.13.3)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.1.1)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-sidekiq (3.1.0)
      rspec-core (~> 3.0, >= 3.0.0)
      sidekiq (>= 2.4.0)
    rspec-support (3.13.2)
    rubocop (1.75.3)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.44.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.44.1)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.31.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    ruby-progressbar (1.13.0)
    ruby2_keywords (0.0.5)
    ruby_parser (3.20.1)
      sexp_processor (~> 4.16)
    rugged (1.9.0)
    sass (3.4.25)
    sass-rails (5.1.0)
      railties (>= 5.2.0)
      sass (~> 3.1)
      sprockets (>= 2.8, < 4.0)
      sprockets-rails (>= 2.0, < 4.0)
      tilt (>= 1.1, < 3)
    sassc (2.4.0)
      ffi (~> 1.9)
    sawyer (0.9.2)
      addressable (>= 2.3.5)
      faraday (>= 0.17.3, < 3)
    scenic (1.7.0)
      activerecord (>= 4.0.0)
      railties (>= 4.0.0)
    scss-lint (0.38.0)
      rainbow (~> 2.0)
      sass (~> 3.4.1)
    sdoc (2.6.1)
      rdoc (>= 5.0)
    securerandom (0.4.1)
    seed_dump (3.3.1)
      activerecord (>= 4)
      activesupport (>= 4)
    sexp_processor (4.17.0)
    shopify_api (14.6.0)
      activesupport
      concurrent-ruby
      hash_diff
      httparty
      jwt
      oj
      openssl
      securerandom
      sorbet-runtime
      zeitwerk (~> 2.5)
    shopify_api_retry (0.2.0)
    shopify_app (22.4.0)
      activeresource
      addressable (~> 2.7)
      jwt (>= 2.2.3)
      rails (> 5.2.1)
      redirect_safely (~> 1.0)
      shopify_api (>= 14.3.0, < 15.0)
      sprockets-rails (>= 2.0.0)
    shopify_graphql (2.0.0)
      activesupport (>= 6.1.0)
      rails (>= 6.1.0)
      shopify_api (>= 13.4)
      shopify_app (>= 19.0)
    sidekiq (7.3.9)
      base64
      connection_pool (>= 2.3.0)
      logger
      rack (>= 2.2.4)
      redis-client (>= 0.22.2)
    signet (0.17.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 3.0)
      multi_json (~> 1.10)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.12.3)
    simplecov_json_formatter (0.1.4)
    slack-notifier (2.4.0)
    sorbet-runtime (0.5.12043)
    sprockets (3.7.5)
      base64
      concurrent-ruby (~> 1.0)
      rack (> 1, < 3)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sqlite3 (1.6.3)
      mini_portile2 (~> 2.8.0)
    sqlite3 (1.6.3-arm64-darwin)
    sqlite3 (1.6.3-x64-mingw-ucrt)
    sqlite3 (1.6.3-x86_64-darwin)
    sqlite3 (1.6.3-x86_64-linux)
    suture (1.1.2)
      backports
      bar-of-progress (>= 0.1.3)
      sqlite3
    table-for (3.7.0)
      rails (>= 3.0.0)
      with_template (~> 0.2.0)
    terminal-table (4.0.0)
      unicode-display_width (>= 1.1.1, < 4)
    thor (1.3.2)
    tilt (2.1.0)
    timeout (0.4.3)
    trailblazer-option (0.1.2)
    turbo-rails (1.5.0)
      actionpack (>= 6.0.0)
      activejob (>= 6.0.0)
      railties (>= 6.0.0)
    turbolinks (5.2.1)
      turbolinks-source (~> 5.2)
    turbolinks-source (5.2.0)
    twitter-bootstrap-rails (5.0.0)
      actionpack (>= 5.0, < 8.0)
      execjs (~> 2.7)
      less-rails (>= 3.0, < 5.0)
      railties (>= 5.0, < 8.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    tzinfo-data (1.2024.2)
      tzinfo (>= 1.0.0)
    uber (0.1.0)
    unaccent (0.4.0)
    unf (0.1.4)
      unf_ext
    unf_ext (0.0.9.1)
    unf_ext (0.0.9.1-x64-mingw-ucrt)
    unf_ext (0.0.9.1-x86-mingw32)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    vite_rails (3.0.14)
      railties (>= 5.1, < 8)
      vite_ruby (~> 3.0, >= 3.2.2)
    vite_ruby (3.3.4)
      dry-cli (>= 0.7, < 2)
      rack-proxy (~> 0.6, >= 0.6.1)
      zeitwerk (~> 2.2)
    warden (1.2.9)
      rack (>= 2.0.9)
    web-console (4.2.1)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webmock (3.18.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.9.1)
    websocket-driver (0.7.7)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    will_paginate (3.3.1)
    with_template (0.2.0)
      blocks (~> 2.8.0)
      rails (>= 3.0.0)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.7.2)

PLATFORMS
  arm64-darwin-23
  arm64-darwin-24
  x64-mingw-ucrt
  x86-mingw32
  x86_64-darwin-21
  x86_64-darwin-22
  x86_64-darwin-23
  x86_64-linux

DEPENDENCIES
  MailchimpTransactional (~> 1.0, >= 1.0.47)
  actioncable
  activerecord-session_store
  audited (~> 5.0)
  bootstrap-datepicker-rails
  bootstrap-editable-rails
  bootstrap-material-design
  bootstrap-sass
  bootstrap_form (= 2.7.0)
  brakeman
  byebug
  capybara
  central_event_logger!
  coffee-rails
  connection_pool (~> 2.5)
  countries
  cross_promotion_app!
  database_cleaner-active_record
  db_fixtures_dump
  debase (~> 0.2.9)
  devise
  dotenv-rails
  factory_bot_rails
  faker
  fasterer
  font-awesome-rails
  gibbon
  google-cloud-storage
  honeybadger
  interactor (~> 3.0)
  jbuilder (~> 2.0)
  jquery-rails
  launchy
  liquid
  liquid-validator
  lograge
  migration_data
  mini_magick
  mock_redis
  oauth2 (= 1.4.7)
  omniauth
  omniauth-google-oauth2
  omniauth-lexoffice (= 0.1.5)
  omniauth-rails_csrf_protection
  parallel
  parallel_tests
  pg
  pronto
  pronto-brakeman
  pronto-eslint_npm
  pronto-rails_best_practices
  pronto-rails_schema
  pronto-rubocop
  pry-nav
  pry-rails
  psych (< 4)
  puma (~> 6.0)
  rack-cors (~> 2.0)
  rack-protection (~> 2)
  rails (= 7.1.5)
  rails-controller-testing
  rails-i18n (~> 7.0, >= 7.0.5)
  rails_12factor
  rails_autoscale_agent
  rails_best_practices
  rails_layout
  redis (~> 4.7.1)
  redis-client (~> 0.22.0)
  redlock (~> 2.0)
  rest-client
  retriable
  rspec-rails
  rspec-sidekiq
  rubocop (~> 1.72)
  rubocop-performance
  rubocop-rails
  sass-rails (~> 5.0)
  scenic
  scss-lint
  sdoc (~> 2.4)
  seed_dump
  shopify_api (~> 14.6.0)
  shopify_api_retry
  shopify_app (~> 22.4.0)
  shopify_billing (= 1.1.0)!
  shopify_graphql (~> 2.0)
  sidekiq (~> 7.3.9)
  simplecov
  slack-notifier
  suture
  table-for
  turbolinks
  twitter-bootstrap-rails
  tzinfo-data
  vite_rails (= 3.0.14)
  vite_ruby (= 3.3.4)
  web-console
  webmock
  will_paginate (~> 3.3, >= 3.3.1)
  zeitwerk (~> 2.6)

RUBY VERSION
   ruby 3.2.7p253

BUNDLED WITH
   2.4.8
