
# README

## Lexware Office Documentation
Public API: https://developers.lexware.io 
Partner API: https://developers.lexware.io/partner

## technical requirements:

- ruby
- postgresql
- redis
- ngrok

## First steps to get the app up and running:

- clone the git repo to your local machine `<NAME_EMAIL>:eshopguidede/lexoffice-integration.git [target folder]`
- activate the right ruby version as defined in the Gemfile (if ruby is installed via version manager watch out for something like rbenv or rvm)
- execute `bundle install` if not installed yet, install it with `gem install bundler`
- execute `yarn install`
- copy the .env.example and fill in your own data
- In RubyMine, create a "Ruby remote debug" configuration (Run -> Edit Configurations -> Add):
  - Remote host: `localhost`
  - Remote port: `1234`
  - Remote root folder: [Local path to your Project]
  - Local port: `26162`
  - Local root folder: [Local path to your Project]

Now you should have a working version of the lexoffice app. \n
The next steps are necessary to get it running in the shopify universe respectively your own shop.

- load the database schema `rails db:schema:load`
- migrate the last database changes `rails db:migrate`
- seed the db with data `rails db:seed`

## make the app accessible in your development shop

- start the app `yarn dev` and follow the instructions on the screen
- In RubyMine, start the remote debugger
- install the app by opening the preview url printed in the terminal

# Linters

Note: linters will scan whole project instead of changed files as it doing on CI pipeline
### Rubocop

Command - `rubocop --config config/rubocop.yml` - not recommended since it will scan whole project

Command - `rubocop <file_path> --config config/rubocop.yml`

### Brakeman

Command - `brakeman`

### Fasterer

Command - `fasterer`


### Rails Best Practices

Command - `rails_best_practices .`

Also recommend to specify path to file or folder

### Reek

Command - `reek` or `reek <path_to_file/path_to_folder>`

# Workers, Jobs and Queues

## Dyno: liveworker
### Queue: live
- SyncOrderJob
- RefundJob
- TahCreationJob
- TenderTransactionJob

## Dyno: jobworker
 queues ordered by priority
### Queue: Default
- SyncAllJob
- UninstallJob
### Queue: doctasks
- FinancialAccountJob
- SendMailJob
### Queue: import
- ImportOrderJob
- ImportRefundJob
- ImportTenderTransactionJob
### Queue: misc
- MailchimpSubscribeJob
- MailchimpUnsubscribeJob
- NotificationsJob

## Dyno: infosyncworker
### Queue: infosync
- DocStatusJob
- InvoiceDataRetrievalJob
- InvoiceDataRetrievalJobTop80
- LexofficeWebhooksJob