APP_HOME = 'https://lexoffice-shopify.eu.ngrok.io/'
ADMIN_APP_NAME=lexoffice-local

SHOPIFY_CLIENT_API_KEY =
SHOPIFY_CLIENT_API_SECRET =

LEXOFFICE_KEY =
LEXOFFICE_SECRET =
LEXOFFICE_SITE = "https://app.lexware-sandbox.de"
LEXOFFICE_API = "https://api.lexware-sandbox.io"
LEXOFFICE_SCOPE = "invoices.read invoices.write contacts.read contacts.write vouchers.read vouchers.write vouchers.sendmail profile.read credit-notes.read credit-notes.write files.read finance.read finance.write transaction-assignment-hint.write"
LEXOFFICE_WEBHOOK_SECRET =

DB_NAME =
DB_USER =
DB_PASSWORD =

MY_REDIS_URL=redis://127.0.0.1:6379/5
REDIS_PROVIDER=MY_REDIS_URL

SIDEKIQ_USERNAME = DAVE
SIDEKIQ_PASSWORD = DAVE

// HELPSCOUT
HELPSCOUT_API_URL=https://docsapi.helpscout.net/v1
HELPSCOUT_API_KEY=
HELPSCOUT_COLLECTION_ID=
HELPSCOUT_TOP10_CATEGORY_ID=
HELPSCOUT_CHANGELOG_ARTICLE_ID=

MANDRILL_APIKEY =
MAILCHIMP_API_KEY =
MAILCHIMP_NEWCUSTOMERS_LIST = "66116e7c76"

HONEYBADGER_LOGGING_PATH="log/honeybadger.log"

IMPORT_PRICE = 99
TEST_CHARGE = true
TRIAL_DAYS = 7

DV_URL = "https://cdn.shopify.com/s/files/1/1490/3000/files/DV_Vereinbarung_lexoffice.pdf?14498566566622324577"
IMPORT_JOB_INTERVAL=
ERROR_MAIL_INTERVAL_HOURS=

//SUPPORT
GOOGLE_OAUTH_CLIENT_ID=
GOOGLE_OAUTH_CLIENT_SECRET=