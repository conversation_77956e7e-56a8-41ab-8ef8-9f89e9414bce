# Learn more about configuring your app at https://shopify.dev/docs/apps/tools/cli/configuration

client_id = "491178b563491f15f0be6bcdd1dc1eda"
name = "Lexware Office"
handle = "lexoffice-integration"
application_url = "https://lexoffice-integration.herokuapp.com/"
embedded = true

[build]
include_config_on_deploy = false

[auth]
redirect_urls = [
  "https://lexoffice-integration.herokuapp.com/auth/shopify/callback",
  "https://admin.shopify.com/"
]

[webhooks]
api_version = "2024-10"

[[webhooks.subscriptions]]
uri = "https://lexoffice-integration.herokuapp.com/customer_data_request"
compliance_topics = [ "customers/data_request" ]

[[webhooks.subscriptions]]
uri = "https://lexoffice-integration.herokuapp.com/customer_redact"
compliance_topics = [ "customers/redact" ]

[[webhooks.subscriptions]]
uri = "https://lexoffice-integration.herokuapp.com/shop_redact"
compliance_topics = [ "shop/redact" ]

[pos]
embedded = false
