import { useCallback, useEffect, useMemo, useState, memo } from "react";
import PropTypes from "prop-types";
import {
  BlockStack,
  Badge,
  Divider,
  EmptyState,
  IndexTable,
  Layout,
  Link,
  Loading,
  Text,
  Pagination,
  SkeletonBodyText,
  useIndexResourceState,
  Button,
  Box,
  Card,
  Tabs,
  Filters,
  Tooltip,
  Icon,
  InlineStack,
  Toast,
} from "@shopify/polaris";
import {
  ClipboardMinor,
  RefreshMajor,
  EmailMajor,
  RefreshMinor,
  EmailNewsletterMajor,
  CancelMinor,
} from "@shopify/polaris-icons";
import i18next from "i18next";
import debounce from "lodash.debounce";
import { useDataQuery } from "../../hooks/useDataQuery";
import i18n from "../../../admin/services/i18n";
import DateRangePicker from "../../../admin/components/Import/DateRangePicker";
import ErrorBoundary from "./ErrorBoundary";
import { useAuthenticatedFetch } from "../../hooks/useAuthenticatedFetch";
import TransferLogTableRow from "./TransferLogTableRow";

// Constants
const TABLE_CONFIG = {
  PAGE_SIZE: 10,
  STATUS_MAP: {
    draft: { tone: "default", progress: "incomplete" },
    open: { tone: "warning", progress: "partiallyComplete" },
    paid: { tone: "success", progress: "complete" },
    paidoff: { tone: "success", progress: "complete" },
    voided: { tone: "critical", progress: "complete" },
  },
  ERROR_TYPES: {
    "ShopifyAPI::Order": "RE:",
    "ShopifyAPI::Refund": "GS:",
    tender_transaction: "TR:",
    "Transaction Assignment Hint": "TAH:",
  },
};

// Utility functions
const parseDate = (inputDate) => {
  const date = new Date(inputDate);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const isEmpty = (value) => {
  if (Array.isArray(value)) return value.length === 0;
  return value === "" || value == null;
};

// PropTypes

// Memoized Components
const ErrorBadge = memo(function ErrorBadge({
  error,
  onRetry,
  onCopy,
  internalErrorTone,
  openBeacon,
}) {
  const { title, error_id, helpscout_id, error_info_internal, shopify_type } = error;
  return (
    <>
      <Text variant="bodyMd" as="span">
        <Button variant={"tertiary"} icon={RefreshMinor} onClick={() => onRetry(error_id)} />
        <Badge tone={"critical"} progress={"complete"}>
          <Link monochrome={true} removeUnderline={true} onClick={() => openBeacon(helpscout_id)}>
            {`${TABLE_CONFIG.ERROR_TYPES[shopify_type] || ""} ${title}`}
          </Link>
        </Badge>
      </Text>
      <Tooltip content={error_info_internal}>
        <Text variant="bodyMd" as="span">
          <Button
            variant={"tertiary"}
            icon={ClipboardMinor}
            onClick={() => onCopy(error_info_internal)}
          />
          <Badge tone={internalErrorTone} progress={"complete"}>
            {error_info_internal.length > 20
              ? error_info_internal.substring(0, 20) + "..."
              : "Error hat keine interne Info"}
          </Badge>
        </Text>
      </Tooltip>
    </>
  );
});
ErrorBadge.displayName = "ErrorBadge";

const DocumentBadge = memo(function DocumentBadge({ document }) {
  const { title, status, target_id, last_action } = document;
  if (!target_id) return null;
  return (
    <InlineStack style={{ padding: "0.5rem" }}>
      <Text variant="bodyMd" as="span" key={target_id}>
        <Badge
          tone={TABLE_CONFIG.STATUS_MAP[status]?.tone || "default"}
          progress={TABLE_CONFIG.STATUS_MAP[status]?.progress || "incomplete"}
        >
          <InlineStack gap={100} blockAlign={"center"}>
            {title || i18next.t("transfer_log.no_title")}
            {last_action === "Send Mail Job Queued" ? (
              <Icon source={EmailNewsletterMajor} />
            ) : last_action === "Mailed" ? (
              <Icon source={EmailMajor} />
            ) : (
              <Icon source={CancelMinor} />
            )}
          </InlineStack>
        </Badge>
      </Text>
    </InlineStack>
  );
});
DocumentBadge.displayName = "DocumentBadge";

const TransactionBadge = memo(function TransactionBadge({ transaction }) {
  const { amount, target_id } = transaction;
  if (!target_id) return null;
  return (
    <Text variant="bodyMd" as="span" key={target_id}>
      <Badge tone={"success"} progress="complete">
        {amount.replace(/\\/g, "")} €
      </Badge>
    </Text>
  );
});
TransactionBadge.displayName = "TransactionBadge";

export function TransferLogTable({ shop, setErrorBannerTone, setShowBanner }) {
  const [currentPage, setCurrentPage] = useState(1);
  const [queryParams, setQueryParams] = useState({
    sort: "",
    filter: "",
    search: "",
    interval: { since: "", until: "" },
  });
  const [retry_all, setRetryAll] = useState(false);
  const [toastActive, setToastActive] = useState(false);
  const [toastMessage, setToastMessage] = useState("");

  const authenticatedFetch = useAuthenticatedFetch();

  useEffect(() => {
    i18n.changeLanguage("de");
  }, [i18n]);

  // Memoized values
  const queryString = useMemo(() => {
    const params = new URLSearchParams({
      page: currentPage,
      sort: queryParams.sort,
      filter: queryParams.filter,
      search: queryParams.search,
      shop: shop,
      start_date: queryParams.interval.since,
      end_date: queryParams.interval.until,
    });
    return `/api/support/transfer_history?${params.toString()}`;
  }, [currentPage, queryParams, shop]);

  const {
    data: transferHistoryData,
    refetch: refetchTransfers,
    isLoading: isLoadingTransfers,
    isRefetching: isRefetchingTranfers,
  } = useDataQuery({ url: queryString });

  const transferHistory = transferHistoryData?.transfers || [];

  // Normalize all IDs to strings for selection logic
  const transferHistoryWithStringIds = transferHistory.map((item) => ({
    ...item,
    id: item.id.toString(),
  }));

  const emptyRowMarkupLoading = (
    <div>
      <SkeletonBodyText rows={6} />
      <SkeletonBodyText rows={6} />
    </div>
  );
  const emptyStateMarkup = (
    <EmptyState
      heading={i18next.t("transfer_log.empty_state.heading")}
      image="https://cdn.shopify.com/s/files/1/0262/4071/2726/files/emptystate-files.png"
    ></EmptyState>
  );
  const tableEmptyMarkup = isLoadingTransfers ? emptyRowMarkupLoading : emptyStateMarkup;

  const resourceName = {
    singular: "orders",
    plural: "orders",
  };

  const {
    selectedResources,
    allResourcesSelected,
    handleSelectionChange,
    removeSelectedResources,
  } = useIndexResourceState(transferHistoryWithStringIds);
  const [queryValue, setQueryValue] = useState(null);

  const handleFilteredDateRemove = useCallback(() => {
    setSelectedDate(null);
    setQueryParams((prevParams) => ({ ...prevParams, interval: { since: "", until: "" } }));
    refetchTransfers();
  }, []);
  const handleQueryValueRemove = useCallback(() => setQueryValue(null), []);
  const handleClearAll = useCallback(() => {
    handleFilteredDateRemove();
    handleQueryValueRemove();
  }, [handleQueryValueRemove, handleFilteredDateRemove]);

  // Memoized handlers
  const handleRetries = useCallback(async () => {
    try {
      const response = await authenticatedFetch("/api/support/retry_transfers", {
        method: "POST",
        body: JSON.stringify({
          order_ids: selectedResources,
          shop,
          retry_all,
        }),
      });
      const data = await response;

      setShowBanner(true);
      setErrorBannerTone(data.success ? "success" : "critical");

      if (data.success) {
        removeSelectedResources(selectedResources);
        refetchTransfers();
      }
    } catch (error) {
      console.error("Error in handleRetries:", error);
      setShowBanner(true);
      setErrorBannerTone("critical");
    }
  }, [selectedResources, shop, retry_all, removeSelectedResources, refetchTransfers]);

  const handleDelete = useCallback(
    async (entity_type, all) => {
      try {
        const response = await authenticatedFetch("/api/support/delete_rows", {
          method: "POST",
          body: JSON.stringify({
            order_ids: selectedResources,
            shop,
            entity_type,
            all,
          }),
        });
        const data = await response;

        setShowBanner(true);
        setErrorBannerTone(data.success ? "success" : "critical");

        if (data.success) {
          removeSelectedResources(selectedResources);
          refetchTransfers();
        }
      } catch (error) {
        console.error("Error in handleDelete:", error);
        setShowBanner(true);
        setErrorBannerTone("critical");
      }
    },
    [selectedResources, shop, removeSelectedResources, refetchTransfers]
  );

  const handleAllRetries = () => {
    setRetryAll(true);
    handleRetries();
  };

  const promotedBulkActions = useMemo(
    () => [
      {
        content: "Selektion neu übertragen",
        onAction: handleRetries,
      },
      {
        content: "Alle Fehler neu übertragen",
        onAction: handleAllRetries,
      },
      {
        content: "Fehler löschen",
        onAction: () => handleDelete("Error", false),
      },
      {
        content: "Alle Fehler löschen",
        onAction: () => {
          if (window.confirm("Wirklich ALLE Fehler für diese/n Händler/in löschen?")) {
            handleDelete("Error", true);
          }
        },
      },
      {
        content: "Sync Infos löschen",
        onAction: () => handleDelete("SyncInfo", false),
      },
      {
        content: "Alle Sync Infos löschen",
        onAction: () => {
          if (window.confirm("Wirklich ALLE SyncInfos für diese/n Händler/in löschen?")) {
            handleDelete("SyncInfo", true);
          }
        },
      },
    ],
    [handleRetries, handleAllRetries, handleDelete]
  );

  const [selectedDate, setSelectedDate] = useState();
  const handleDateSelection = useCallback((range) => {
    if (range === null) return;
    setSelectedDate(range);
    setQueryParams((prevParams) => ({
      ...prevParams,
      interval: {
        since: parseDate(range.period.since),
        until: parseDate(range.period.until),
      },
    }));
    refetchTransfers();
  });

  const handleErrorCopy = useCallback((errorText) => {
    navigator.clipboard.writeText(errorText);
    setToastMessage("Fehlerinfo kopiert!");
    setToastActive(true);
  }, []);

  const filters = useMemo(
    () => [
      {
        key: "date",
        label: "Erstellungsdatum",
        filter: (
          <DateRangePicker
            title={"Übertragungszeitraum"}
            onChange={handleDateSelection}
            defaultRange={selectedDate}
          />
        ),
        shortcut: true,
      },
    ],
    [handleDateSelection, selectedDate]
  );

  const appliedFilters = useMemo(() => {
    return selectedDate && !isEmpty(selectedDate)
      ? [
          {
            key: "date",
            label: disambiguateLabel(
              "Erstellungsdatum",
              `${parseDate(selectedDate.period.since)} - ${parseDate(selectedDate.period.until)}`
            ),
            onRemove: handleFilteredDateRemove,
          },
        ]
      : [];
  }, [selectedDate, handleFilteredDateRemove]);

  // Filter-Tabs
  const [selectedTab, setSelectedTab] = useState(0);

  const handleTabChange = useCallback((selectedTabIndex) => {
    setSelectedTab(selectedTabIndex);
    setCurrentPage(1);
    setQueryParams((prevParams) => ({ ...prevParams, filter: tabs[selectedTabIndex].filter }));
  }, []);

  const handleSearchChange = (searchValue) => {
    setCurrentPage(1);
    setQueryParams((prevParams) => ({ ...prevParams, search: searchValue }));
  };
  const resetSearch = useCallback(() => {
    setQueryParams((prevParams) => ({ ...prevParams, search: "" }));
  }, []);

  const handleSearchChangeDebounced = useCallback(debounce(handleSearchChange, 500), []);

  const tabs = [
    {
      id: "tab-all",
      content: i18next.t("transfer_log.tabs.all"),
      filter: "",
    },
    {
      id: "tab-invoice",
      filter: "Invoice",
      content: i18next.t("transfer_log.tabs.invoice"),
    },
    {
      id: "tab-credit-note",
      filter: "Refund",
      content: i18next.t("transfer_log.tabs.credit_note"),
    },
    {
      id: "tab-transaction",
      filter: "Transaction",
      content: i18next.t("transfer_log.tabs.transaction"),
    },
    {
      id: "tab-tahs",
      filter: "Transaction Assignment Hint",
      content: "Mit TAHs",
    },
    {
      id: "tab-import",
      filter: "Import",
      content: "Importiert",
    },
    {
      id: "tab-errors",
      filter: "Error",
      content: i18next.t("transfer_log.tabs.errors"),
    },
  ];

  // Add openBeacon function for support area
  const openBeacon = (articleId) => {
    if (window.Beacon) {
      if (articleId === "NULL" || !articleId) {
        window.Beacon("open");
        window.Beacon("navigate", "/ask/");
      } else {
        window.Beacon("article", articleId);
      }
    }
  };

  // Add this before rowMarkup
  const retrySingleError = () => {
    setRetryAll(false);
    handleRetries().then(() => refetchTransfers());
  };

  const rowMarkup = useMemo(
    () =>
      transferHistoryWithStringIds.map(
        ({ id, order_id, date, invoices, credit_notes, transactions, tah, errors }, index) => (
          <TransferLogTableRow
            key={id}
            id={id}
            order_id={order_id}
            date={date}
            invoices={invoices}
            credit_notes={credit_notes}
            transactions={transactions}
            tah={tah}
            errors={errors}
            selected={selectedResources.includes(id)}
            index={index}
            shop={shop}
            retrySingleError={retrySingleError}
            handleErrorCopy={handleErrorCopy}
            openBeacon={openBeacon}
          />
        )
      ),
    [
      transferHistoryWithStringIds,
      selectedResources,
      shop,
      retrySingleError,
      handleErrorCopy,
      openBeacon,
    ]
  );

  return (
    <ErrorBoundary>
      <div>
        {isLoadingTransfers && <Loading />}
        <Divider />
        <Layout>
          <Layout.Section>
            <Card padding="200">
              <Tabs tabs={tabs} selected={selectedTab} onSelect={handleTabChange} />
              <Filters
                queryValue={queryValue}
                queryPlaceholder={i18next.t("transfer_log.search_placeholder")}
                filters={filters}
                appliedFilters={appliedFilters}
                onQueryChange={handleSearchChangeDebounced}
                onQueryClear={resetSearch}
                onClearAll={handleClearAll}
              >
                <Box padding="300">
                  <Button
                    onClick={refetchTransfers}
                    icon={RefreshMajor}
                    loading={isRefetchingTranfers}
                  />
                </Box>
              </Filters>
              <IndexTable
                resourceName={resourceName}
                emptyState={tableEmptyMarkup}
                itemCount={transferHistory.length}
                selectedItemsCount={allResourcesSelected ? "All" : selectedResources.length}
                onSelectionChange={handleSelectionChange}
                hasMoreItems
                promotedBulkActions={promotedBulkActions}
                lastColumnSticky
                loading={isLoadingTransfers || isRefetchingTranfers}
                headings={[
                  { title: "ID" },
                  { title: i18next.t("transfer_log.table_headers.order") },
                  { title: "Shopify " + i18next.t("transfer_log.table_headers.date") },
                  { title: i18next.t("transfer_log.invoices") },
                  { title: i18next.t("transfer_log.credit_notes") },
                  { title: i18next.t("transfer_log.transactions") },
                  { title: "TAHs" },
                  { title: i18next.t("transfer_log.table_headers.errors") },
                ]}
              >
                {rowMarkup}
              </IndexTable>

              {transferHistoryData?.totalPages > 1 && (
                <div style={{ padding: "1.5rem" }}>
                  <BlockStack inlineAlign="center">
                    <Pagination
                      label={i18next.t("will_paginate.pagination_label", {
                        page: currentPage,
                        total: transferHistoryData?.totalPages,
                      })}
                      hasPrevious={currentPage > 1}
                      previousKeys={[74]}
                      previousTooltip={i18next.t("will_paginate.previous_label")}
                      onPrevious={() => setCurrentPage((prev) => prev - 1)}
                      hasNext={currentPage < transferHistoryData?.totalPages}
                      nextKeys={[75]}
                      nextTooltip={i18next.t("will_paginate.next_label")}
                      onNext={() => setCurrentPage((prev) => prev + 1)}
                    />
                  </BlockStack>
                </div>
              )}
            </Card>
          </Layout.Section>
        </Layout>
        {toastActive && <Toast content={toastMessage} onDismiss={() => setToastActive(false)} />}
      </div>
    </ErrorBoundary>
  );

  function disambiguateLabel(key, value) {
    switch (key) {
      case "date":
        return `Datum: ${value}`;
      default:
        return value;
    }
  }
}

TransferLogTable.propTypes = {
  shop: PropTypes.string.isRequired,
  setErrorBannerTone: PropTypes.func.isRequired,
  setShowBanner: PropTypes.func.isRequired,
};

export default memo(TransferLogTable);
