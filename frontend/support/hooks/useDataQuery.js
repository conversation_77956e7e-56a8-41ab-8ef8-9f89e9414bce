import { useMemo } from "react";
import { useQuery } from "react-query";

export const useDataQuery = (options) => {
  const {
    url,
    fetchInit = {},
    reactQueryOptions = {},
  } = typeof options === "string" ? { url: options } : options;

  const query = useMemo(
    () => async () => {
      const response = await fetch(url, fetchInit);
      return response.json();
    },
    [url, fetchInit]
  );

  return useQuery(url, query, reactQueryOptions);
};
