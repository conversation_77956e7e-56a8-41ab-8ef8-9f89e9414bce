import React, { use<PERSON><PERSON>back, useMemo, useState, memo } from "react";
import {
    AppP<PERSON>ider,
    Button,
    Divider,
    InlineGrid,
    InlineStack,
    Link,
    Loading,
    Page,
    Select,
    Tabs
} from "@shopify/polaris";
import { useLocation, useNavigate } from "react-router-dom";
import ShopInfosAndSettings from "../components/Support/ShopInfosAndSettings";
import ShopErrors from "../components/Support/ShopErrors";
import Audits from "../components/Support/Audits";
import { useDataQuery } from "../hooks/useDataQuery";
import Import from "../components/Support/Import";
import { getCsrfToken } from "../hooks/getCsrfToken";
import translations from "@shopify/polaris/locales/en.json";
import {useAuthenticatedFetch} from "../hooks/useAuthenticatedFetch";
import NotFound from "./NotFound";

// Constants
const API_ENDPOINTS = {
    MERCHANT: '/api/support/merchant',
    ACTIVATE_PLAN: '/api/support/activate_plan',
    TOGGLE_TEST_SHOP: '/api/support/toggle_test_shop'
};

const TABS = [
    {
        id: 'shop',
        content: 'Shop Infos and Settings',
        accessibilityLabel: 'Shop',
        panelID: 'shop-and-settings',
    },
    {
        id: 'sync',
        content: 'Übertragungslog',
        accessibilityLabel: 'Synchronization',
        panelID: 'shop-synchronization'
    },
    {
        id: 'audits',
        content: 'Audits',
        accessibilityLabel: 'Audits',
        panelID: 'shop-audits',
    },
    {
        id: 'import',
        content: 'Import',
        accessibilityLabel: 'Import',
        panelID: 'shop-import',
    }
];

const NAVIGATION_LINKS = [
    { url: '/support/coupons', label: 'Coupons' },
    { url: '/support/beacons', label: 'Beacons' },
    { url: '/support/codeSnippets', label: 'Code Snippets' }
];

const Merchant = memo(() => {
    const location = useLocation();
    const navigate = useNavigate();
    const queryParams = new URLSearchParams(location.search);
    const shop = queryParams.get('shop');
    const authenticatedFetch = useAuthenticatedFetch();

    if (!shop) {
        return <NotFound/>;
    }

    // State
    const [selected, setSelected] = useState(0);

    // Data fetching
    const { 
        data: shopData,
        isLoading: isLoadingShopData,
        refetch: refetchShopData 
    } = useDataQuery({
        url: `${API_ENDPOINTS.MERCHANT}?shop=${shop}`
    });

    // Handlers
    const handleTabChange = useCallback((selectedTabIndex) => {
        setSelected(selectedTabIndex);
    }, []);

    const handlePlan = useCallback(async (planType) => {
        try {
            await authenticatedFetch(API_ENDPOINTS.ACTIVATE_PLAN, {
                method: "POST",
                body: JSON.stringify({
                    plan: planType,
                    shop: shop
                })
            });
            refetchShopData();
        } catch (error) {
            console.error('Error handling the plan:', error);
        }
    }, [shop, refetchShopData]);

    const handleDeactivatePlan = useCallback(() => {
        handlePlan('app');
    }, [handlePlan]);

    const handleActivateImport = useCallback(() => {
        handlePlan('import');
    }, [handlePlan]);

    const handleTestShop = useCallback(async () => {
        try {
            await authenticatedFetch(API_ENDPOINTS.TOGGLE_TEST_SHOP, {
                method: "POST",
                body: JSON.stringify({
                    shop: shop
                })
            });
            refetchShopData();
        } catch (error) {
            console.error('Error toggling test shop:', error);
        }
    }, [shop, refetchShopData]);

    const navigationLinks = useMemo(() => (
        <InlineGrid gap={100} columns={['oneThird', 'oneThird', 'oneThird']}>
            {NAVIGATION_LINKS.map(link => (
                <Link key={link.url} monochrome target="_blank" url={link.url}>
                    {link.label}
                </Link>
            ))}
        </InlineGrid>
    ), []);
    const actionButtons = useMemo(() => (
        <InlineGrid gap={500} columns={['oneThird', 'oneThird', 'oneThird']}>
            <Button
                onClick={handleTestShop}
            >
                Testshop {shopData?.shopify_shop_info?.internal_test_shop ? 'deaktivieren' : 'aktivieren'}
            </Button>
            <Button
                onClick={handleActivateImport}
                disabled={shopData?.import_plan}
            >
                Import freischalten
            </Button>
            <Button
                onClick={handleDeactivatePlan}
                disabled={!shopData?.current_plan}
            >
                App ausschalten
            </Button>
        </InlineGrid>
    ), [handleActivateImport, handleDeactivatePlan, shopData]);

    // Render helpers
    const renderTabComponent = useCallback(() => {
        if (!shopData) return null;

        switch (selected) {
            case 0:
                return <ShopInfosAndSettings shopData={shopData} refetch={refetchShopData} />;
            case 1:
                return <ShopErrors shopData={shopData} />;
            case 2:
                return <Audits audits={shopData.audits} />;
            case 3:
                return <Import shopData={shopData} refetch={refetchShopData} />;
            default:
                return null;
        }
    }, [selected, shopData, refetchShopData]);

    if (isLoadingShopData) {
        return <Loading />;
    }

    return (
        <AppProvider i18n={translations}>
            <Page 
                title={`Lexware Office Support: ${shopData?.shop_info[0]?.name}`} 
                fullWidth
                subtitle="Eshop-Guide Apps Support UI"
                backAction={{ onAction: () => navigate('/support') }}
                secondaryActions={
                    <InlineStack gap={500} blockAlign="center">
                        {navigationLinks}
                        {actionButtons}
                    </InlineStack>
                }
            >
                <Divider />
                <Tabs 
                    tabs={TABS} 
                    selected={selected} 
                    onSelect={handleTabChange}
                >
                    {renderTabComponent()}
                </Tabs>
            </Page>
        </AppProvider>
    );
});

export default Merchant;