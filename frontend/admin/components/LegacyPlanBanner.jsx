import { useContext } from "react";
import { useTranslation } from "react-i18next";
import { Banner } from "@shopify/polaris";
import { EshopGuideContext } from "@/shared_components/providers/EshopGuideProvider";

export default function LegacyPlanBanner() {
  const { shopInfo } = useContext(EshopGuideContext);
  const { t } = useTranslation();

  const { billing_plan_name, legacy } = shopInfo?.billing || {};

  if (!billing_plan_name || !legacy) {
    return null;
  }

  return (
    <Banner title={t("billing.legacyPlanBanner.title")} tone="info">
      {t("billing.legacyPlanBanner.text", {
        planName: billing_plan_name,
      })}
    </Banner>
  );
}
