import React, { useMemo, useState } from "react";
import { Card, FormLayout, Layout, Checkbox, ChoiceList, TextField, Link } from "@shopify/polaris";
import TextFieldPlaceholderAndPreview from "@/shared_components/components/TextFieldPlaceholderAndPreview.jsx";
import { Trans, useTranslation } from "react-i18next";
import CustomTextField from "../CustomTextField.jsx";
import SanitizedTextField from "../SanitizedTextField.jsx";
import BeaconMessages from "@/shared_components/components/BeaconMessages";
import OrderTagsInput from "../OrderTagsInput";
import AttentionBanner from "../AttentionBanner";
import { useViewMode } from "../providers/ViewModeProvider.jsx";
import PrintLayoutsSettings from "./PrintLayoutsSettings";
import Paywall from "../Paywall.jsx";

/*
  Invoice settings Tab
 */
export default function InvoiceSettings({
  shopSettings,
  onSettingsChange,
  beaconMessages,
  shopDomain,
}) {
  const { t } = useTranslation();
  const { isDetailView } = useViewMode();

  // Show shipping min days if shipping type is deliveryperiod or serviceperiod
  const showShippingMinDays = useMemo(() => {
    return (
      shopSettings.shipping_type === "deliveryperiod" ||
      shopSettings.shipping_type === "serviceperiod"
    );
  }, [shopSettings.shipping_type]);

  const areShippingDaysValid = useMemo(() => {
    if ((shopSettings.shipping_type || "").includes("period")) {
      return (
        shopSettings.shipping_max_days > shopSettings.shipping_min_days &&
        shopSettings.shipping_min_days >= 0
      );
    }

    return shopSettings.shipping_max_days >= 0;
  }, [shopSettings]);

  return (
    <>
      <Layout>
        <Layout.AnnotatedSection
          title={
            <BeaconMessages beacons={beaconMessages} title={t("settings.invoices.general.title")} />
          }
          description={t("settings.invoices.general.description")}
        >
          <Card>
            <FormLayout>
              <Checkbox
                id={"create_invoices"}
                label={t("settings.invoices.general.label")}
                checked={shopSettings.create_invoices}
                onChange={onSettingsChange}
                helpText={t("settings.invoices.general.help")}
              />
            </FormLayout>
          </Card>
        </Layout.AnnotatedSection>

        <Layout.AnnotatedSection
          title={t("settings.invoices.layouts.title")}
          description={t("settings.invoices.layouts.description")}
        >
          <PrintLayoutsSettings onSettingsChange={onSettingsChange} type={"invoices"} />
        </Layout.AnnotatedSection>

        <Layout.AnnotatedSection
          title={t("settings.invoices.invoice_timing.title")}
          description={t("settings.invoices.invoice_timing.description")}
        >
          <Card>
            <FormLayout>
              <Checkbox
                id={"mark_due_immediately"}
                label={t("settings.invoices.invoice_timing.label")}
                checked={shopSettings.mark_due_immediately}
                onChange={onSettingsChange}
                helpText={t("settings.invoices.invoice_timing.help")}
              />
              <ChoiceList
                title={t("settings.invoices.invoice_timing.timing")}
                choices={[
                  {
                    label: t("settings.invoices.invoice_timing.choices.create"),
                    value: "orders/create",
                  },
                  {
                    label: t("settings.invoices.invoice_timing.choices.fulfilled"),
                    value: "orders/fulfilled",
                  },
                ]}
                selected={[shopSettings.invoice_timing]}
                onChange={(value) => onSettingsChange(value[0], "invoice_timing")}
              />
            </FormLayout>

            <AttentionBanner shopSettings={shopSettings} />
          </Card>
        </Layout.AnnotatedSection>

        <Layout.AnnotatedSection
          title={t("settings.invoices.invoices_exclusion.title")}
          description={t("settings.invoices.invoices_exclusion.description")}
        >
          <Paywall feature="exclude_orders_by_tag">
            <Card>
              <FormLayout>
                <OrderTagsInput
                  onSettingsChange={onSettingsChange}
                  settings={shopSettings}
                  type={"order"}
                />
              </FormLayout>
            </Card>
          </Paywall>
        </Layout.AnnotatedSection>

        {isDetailView && (
          <>
            <Layout.AnnotatedSection
              title={t("settings.invoices.SKUs.title")}
              description={t("settings.invoices.SKUs.description")}
            >
              <Card>
                <FormLayout>
                  <Checkbox
                    id={"use_SKUs"}
                    label={t("settings.invoices.SKUs.label")}
                    checked={shopSettings.use_SKUs}
                    onChange={onSettingsChange}
                    helpText={t("settings.invoices.SKUs.help")}
                  />
                </FormLayout>
              </Card>
            </Layout.AnnotatedSection>

            <Layout.AnnotatedSection
              title={t("settings.invoices.addresses.title")}
              description={t("settings.invoices.addresses.description")}
            >
              <Card>
                <FormLayout>
                  <Checkbox
                    id={"use_shipping_address_for_invoices"}
                    label={t("settings.invoices.addresses.label")}
                    checked={shopSettings.use_shipping_address_for_invoices}
                    onChange={onSettingsChange}
                    helpText={
                      <Trans
                        i18nKey={`settings.invoices.addresses.help`}
                        components={{
                          a: <Link onClick={() => window.Beacon("article", "52")} />,
                        }}
                      />
                    }
                  />
                </FormLayout>
              </Card>
            </Layout.AnnotatedSection>
          </>
        )}

        <Layout.AnnotatedSection
          title={t("settings.invoices.shipping_types.title")}
          description={t("settings.invoices.shipping_types.description")}
        >
          <Card>
            <FormLayout>
              <ChoiceList
                title={t("settings.invoices.shipping_types.types")}
                choices={[
                  {
                    label: t("settings.invoices.shipping_types.choices.option1"),
                    value: "delivery",
                  },
                  {
                    label: t("settings.invoices.shipping_types.choices.option2"),
                    value: "service",
                  },
                  {
                    label: t("settings.invoices.shipping_types.choices.option3"),
                    value: "deliveryperiod",
                  },
                  {
                    label: t("settings.invoices.shipping_types.choices.option4"),
                    value: "serviceperiod",
                  },
                  { label: t("settings.invoices.shipping_types.choices.option5"), value: "none" },
                ]}
                selected={[shopSettings.shipping_type || "none"]}
                onChange={(value) => onSettingsChange(value[0], "shipping_type")}
              />
              {showShippingMinDays && (
                <TextField
                  id={"shipping_min_days"}
                  value={shopSettings.shipping_min_days}
                  type="number"
                  onChange={onSettingsChange}
                  prefix={t("settings.invoices.shipping_types.earliest_delivery")}
                  suffix={t("settings.invoices.shipping_types.shipping_after_days")}
                  autoComplete="off"
                  min={0}
                  error={!areShippingDaysValid}
                />
              )}
              <TextField
                id={"shipping_max_days"}
                type="number"
                value={shopSettings.shipping_max_days}
                onChange={onSettingsChange}
                prefix={t("settings.invoices.shipping_types.latest_delivery")}
                suffix={t("settings.invoices.shipping_types.shipping_after_days")}
                autoComplete="off"
                min={0}
                error={
                  !areShippingDaysValid
                    ? t("settings.invoices.shipping_types.shipping_days_error")
                    : null
                }
              />
            </FormLayout>
          </Card>
        </Layout.AnnotatedSection>

        {isDetailView && (
          <Layout.AnnotatedSection
            title={t("settings.invoices.customer_accounts.title")}
            description={t("settings.invoices.customer_accounts.description")}
          >
            <Paywall feature="customer_accounts_invoice_download">
              <Card>
                <FormLayout>
                  <Trans
                    i18nKey={"settings.invoices.customer_accounts.help"}
                    components={{
                      a: (
                        <Link
                          target="_blank"
                          url={`https://${shopDomain}/admin/themes/current/editor`}
                        />
                      ),
                    }}
                  />
                </FormLayout>
              </Card>
            </Paywall>
          </Layout.AnnotatedSection>
        )}

        <Layout.AnnotatedSection
          title={t("settings.invoices.invoice_text.title")}
          description={t("settings.invoices.invoice_text.description")}
        >
          <Card>
            <FormLayout>
              <SanitizedTextField
                id={"invoice_title"}
                label={t("settings.invoices.invoice_text.title_label")}
                labelAction={{
                  content: (
                    <TextFieldPlaceholderAndPreview
                      textFieldId="invoice_title"
                      textFieldValue={shopSettings.invoice_title}
                      handleSettingsChange={onSettingsChange}
                      showPreview={false}
                    />
                  ),
                }}
                value={shopSettings.invoice_title}
                onChange={onSettingsChange}
                maxResolvedLength={25}
                autoComplete="off"
                helpText={t("settings.invoices.invoice_text.title_help")}
              />
              <CustomTextField
                id={"invoice_pretext"}
                label={t("settings.invoices.invoice_text.pretext_label")}
                labelAction={{
                  content: (
                    <TextFieldPlaceholderAndPreview
                      textFieldId="invoice_pretext"
                      textFieldValue={shopSettings.invoice_pretext}
                      handleSettingsChange={onSettingsChange}
                    />
                  ),
                }}
                value={shopSettings.invoice_pretext}
                autoComplete="off"
                multiline={4}
                maxLength={1800}
                showCharacterCount
                onChange={onSettingsChange}
                helpText={t("settings.invoices.invoice_text.pretext_help")}
              />
              <CustomTextField
                id={"invoice_posttext"}
                label={t("settings.invoices.invoice_text.posttext_label")}
                labelAction={{
                  content: (
                    <TextFieldPlaceholderAndPreview
                      textFieldId="invoice_posttext"
                      textFieldValue={shopSettings.invoice_posttext}
                      handleSettingsChange={onSettingsChange}
                    />
                  ),
                }}
                value={shopSettings.invoice_posttext || ""}
                autoComplete="off"
                multiline={4}
                maxLength={1800}
                showCharacterCount
                onChange={onSettingsChange}
                helpText={t("settings.invoices.invoice_text.posttext_help")}
              />
            </FormLayout>
          </Card>
        </Layout.AnnotatedSection>
      </Layout>
    </>
  );
}
