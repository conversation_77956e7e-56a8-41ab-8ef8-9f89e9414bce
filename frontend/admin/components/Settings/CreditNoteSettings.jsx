import React from "react";
import { Layout, Card, FormLayout, Checkbox } from "@shopify/polaris";
import TextFieldPlaceholderAndPreview from "@/shared_components/components/TextFieldPlaceholderAndPreview.jsx";
import { useTranslation } from "react-i18next";
import CustomTextField from "../CustomTextField.jsx";
import SanitizedTextField from "../SanitizedTextField.jsx";
import BeaconMessages from "@/shared_components/components/BeaconMessages";
import PrintLayoutsSettings from "./PrintLayoutsSettings";

/*
  Credit note settings tab
 */
export default function CreditNoteSettings({ shopSettings, onSettingsChange, beaconMessages }) {
  const { t } = useTranslation();

  return (
    <>
      <Layout>
        <Layout.AnnotatedSection
          title={
            <BeaconMessages
              beacons={beaconMessages}
              title={t("settings.refunds.refund_card.title")}
            />
          }
          description={t("settings.refunds.refund_card.description")}
        >
          <Card>
            <FormLayout>
              <Checkbox
                id={"create_refunds"}
                label={t("settings.refunds.refund_card.label")}
                checked={shopSettings.create_refunds}
                onChange={onSettingsChange}
                helpText={t("settings.refunds.refund_card.help")}
              />
            </FormLayout>
          </Card>
        </Layout.AnnotatedSection>

        <Layout.AnnotatedSection
          title={t("settings.refunds.layouts.title")}
          description={t("settings.refunds.layouts.description")}
        >
          <PrintLayoutsSettings onSettingsChange={onSettingsChange} type={"refunds"} />
        </Layout.AnnotatedSection>

        <Layout.AnnotatedSection
          title={t("settings.refunds.text_card.title")}
          description={t("settings.refunds.text_card.description")}
        >
          <Card>
            <FormLayout>
              <SanitizedTextField
                id={"refund_title"}
                value={shopSettings.refund_title || ""}
                label={t("settings.refunds.text_card.titel_label")}
                labelAction={{
                  content: (
                    <TextFieldPlaceholderAndPreview
                      textFieldId="refund_title"
                      textFieldValue={shopSettings.refund_title}
                      handleSettingsChange={onSettingsChange}
                    />
                  ),
                }}
                maxResolvedLength={25}
                onChange={onSettingsChange}
                helpText={t("settings.refunds.text_card.titel_help")}
              />
              <CustomTextField
                id={"refund_pretext"}
                value={shopSettings.refund_pretext || ""}
                label={t("settings.refunds.text_card.pretext_label")}
                labelAction={{
                  content: (
                    <TextFieldPlaceholderAndPreview
                      textFieldId="refund_pretext"
                      textFieldValue={shopSettings.refund_pretext}
                      handleSettingsChange={onSettingsChange}
                    />
                  ),
                }}
                multiline={4}
                maxLength={1800}
                showCharacterCount
                onChange={onSettingsChange}
                helpText={t("settings.refunds.text_card.pretext_help")}
              />
              <CustomTextField
                id={"refund_posttext"}
                value={shopSettings.refund_posttext || ""}
                label={t("settings.refunds.text_card.posttext_label")}
                labelAction={{
                  content: (
                    <TextFieldPlaceholderAndPreview
                      textFieldId="refund_posttext"
                      textFieldValue={shopSettings.refund_posttext}
                      handleSettingsChange={onSettingsChange}
                    />
                  ),
                }}
                multiline={4}
                maxLength={1800}
                showCharacterCount
                onChange={onSettingsChange}
                helpText={t("settings.refunds.text_card.posttext_help")}
              />
            </FormLayout>
          </Card>
        </Layout.AnnotatedSection>
      </Layout>
    </>
  );
}
