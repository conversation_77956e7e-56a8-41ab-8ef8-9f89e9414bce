import { createContext, useContext, useState } from "react";
import { Frame, Loading, Page, BlockStack, Text, Link } from "@shopify/polaris";
import { EshopGuideContext } from "@/shared_components/providers/EshopGuideProvider";
import HelpFooter from "@/shared_components/components/HelpFooter";
import { Trans, useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import CouponsCard from "../components/CouponsCard.jsx";
import AvailableBillingPlans from "../components/PlansAndCoupons/AvailableBillingPlans.jsx";
import { RemainingTrialDaysBanner } from "../components/PlansAndCoupons/RemainingTrialDaysBanner.jsx";
import PlanMismatchBanner from "../components/PlanMismatchBanner.jsx";
import HelpArticle from "../components/HelpArticle";
import LegacyPlanBanner from "../components/LegacyPlanBanner.jsx";

export const PlansAndCouponsContext = createContext();

const PlansAndCouponsProvider = ({ children }) => {
  const [activeCouponCode, setActiveCouponCode] = useState("");

  return (
    <PlansAndCouponsContext.Provider value={{ activeCouponCode, setActiveCouponCode }}>
      {children}
    </PlansAndCouponsContext.Provider>
  );
};

export default function PlansAndCoupons() {
  const { shopInfo } = useContext(EshopGuideContext);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { plan_active } = shopInfo?.billing || {};

  return (
    <Frame>
      {!shopInfo && <Loading />}
      <Page
        title={t("billing.plans_and_coupons")}
        subtitle={t("general.app_name")}
        backAction={{ content: "Back", onAction: () => navigate(-1) }}
      >
        <BlockStack gap="800">
          <PlansAndCouponsProvider>
            <BlockStack gap="0">
              <LegacyPlanBanner />
              <PlanMismatchBanner />
              <RemainingTrialDaysBanner />
            </BlockStack>
            <BlockStack gap="400">
              <Text variant="headingLg" alignment="center">
                {t("billing.available_plans")}
              </Text>
              <AvailableBillingPlans />
              <BlockStack gap="50">
                <Text alignment="center">
                  <Trans
                    i18nKey="billing.accept_terms"
                    components={{
                      a: (
                        <Link
                          target="_blank"
                          onClick={() => {
                            window.Beacon("open");
                            window.Beacon("article", "423");
                          }}
                        />
                      ),
                    }}
                  />
                </Text>
                {!plan_active && (
                  <Text alignment="center"> {t("billing.import_plan_not_available")}</Text>
                )}
              </BlockStack>
              <CouponsCard />
              <HelpArticle id="6686a05ad5f6cf2baa1f15d5" />
            </BlockStack>
          </PlansAndCouponsProvider>
        </BlockStack>
        <HelpFooter />
      </Page>
    </Frame>
  );
}
