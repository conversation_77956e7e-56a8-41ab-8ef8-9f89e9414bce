{"name": "frontend", "version": "1.0.0", "description": "[![License: MIT](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE.md)", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@emotion/react": "^11.13.0", "@honeybadger-io/js": "^6.9.3", "@honeybadger-io/react": "^6.1.23", "@rails/actioncable": "^7.2.0", "@shopify/app-bridge": "^3.1.0", "@shopify/app-bridge-react": "^3.1.0", "@shopify/app-bridge-utils": "^3.1.0", "@shopify/polaris": "^12.0.0", "@shopify/polaris-icons": "^6.16.0", "@shopify/stylelint-polaris": "^5.1.0", "@uiw/react-textarea-code-editor": "^3.0.2", "html-react-parser": "^5.1.12", "i18next": "^23.12.3", "lodash.debounce": "^4.0.8", "prop-types": "^15.8.1", "react": "^18.3.1", "react-awesome-reveal": "^4.2.13", "react-dom": "^18.3.1", "react-html-parser": "^2.0.2", "react-i18next": "^15.0.1", "react-json-view": "^1.21.3", "react-query": "^3.39.3", "react-router-dom": "^6.26.0", "react-youtube": "^10.1.0", "sass": "^1.77.8"}, "devDependencies": {"@vitejs/plugin-react": "^4.3.1", "vite": "4.3.0", "vite-plugin-rails": "^0.5.0", "vite-plugin-ruby": "3.2.0"}}