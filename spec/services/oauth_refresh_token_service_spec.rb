# frozen_string_literal: true

require "rails_helper"

RSpec.describe OAuthRefreshTokenService do
  describe "#token_expired?" do
    context "when token expired" do
      let(:shop) { FactoryBot.create(:shop, :with_expired_token) }
      let(:service) { OAuthRefreshTokenService.new(shop) }

      it "should return true" do
        expect(service.send(:token_expired?)).to be_truthy
      end
    end

    context "when token is not expired" do
      let(:shop) { FactoryBot.create(:shop) }
      let(:service) { OAuthRefreshTokenService.new(shop) }

      it "should return false" do
        expect(service.send(:token_expired?)).to be_falsey
      end
    end
  end

  describe "#forced?" do
    let(:shop) { FactoryBot.create(:shop, :with_expired_token) }

    context "when forced" do
      let(:service) { OAuthRefreshTokenService.new(shop, true) }

      it "should return true" do
        expect(service.forced?).to be_truthy
      end
    end

    context "when doesnt forced" do
      let(:service) { OAuthRefreshTokenService.new(shop) }

      it "should return false" do
        expect(service.forced?).to be_falsey
      end
    end
  end

  describe "#lexoffice_token_expires_at" do
    let(:shop) { FactoryBot.create(:shop, :with_expired_token) }
    let(:service) { OAuthRefreshTokenService.new(shop) }

    it "should return correct value" do
      expect(service.send(:lexoffice_token_expires_at)).to eq shop.lexoffice_token_expires_at
    end
  end

  describe "#lexoffice_refresh_token" do
    let(:shop) { FactoryBot.create(:shop, :with_expired_token) }
    let(:service) { OAuthRefreshTokenService.new(shop) }

    it "should return correct value" do
      expect(service.send(:lexoffice_refresh_token)).to eq shop.lexoffice_refresh_token
    end
  end

  describe "#call" do
    context "when token is not expired" do
      let(:shop) { FactoryBot.create(:shop) }
      let(:service) { OAuthRefreshTokenService.new(shop) }

      it "should not call refresh_token!" do
        expect(service).not_to receive(:refresh_token!)
        service.call
      end

      context "when forced" do
        let(:service) { OAuthRefreshTokenService.new(shop, true) }

        before do
          stub_token_refresh
        end

        it "should call refresh_token!" do
          expect(service).to receive(:refresh_token!)
          service.call
        end
      end
    end

    context "when token expired" do
      let(:shop) { FactoryBot.create(:shop, :with_expired_token) }
      let(:service) { OAuthRefreshTokenService.new(shop) }

      before do
        stub_token_refresh
      end

      it "should call refresh_token!" do
        expect(service).to receive(:refresh_token!)
        service.call
      end

      context "when forced" do
        let(:service) { OAuthRefreshTokenService.new(shop, true) }

        it "should call refresh_token!" do
          expect(service).to receive(:refresh_token!)
          service.call
        end
      end
    end
  end

  describe "#refresh_token!" do
    let(:shop) { FactoryBot.create(:shop) }
    let(:service) { OAuthRefreshTokenService.new(shop) }

    context "when token refresh is successful" do
      let(:access_token) { SecureRandom.hex(15) }
      let(:refresh_token) { SecureRandom.hex(15) }
      let(:expires_in) { 3600 }
      let(:successful_response) do
        {
          access_token: access_token,
          refresh_token: refresh_token,
          expires_in: expires_in
        }.to_json
      end

      before do
        token_service_double = double("Lexoffice::TokenService")
        allow(Lexoffice::TokenService).to receive(:new).and_return(token_service_double)
        allow(token_service_double).to receive(:call).and_return(successful_response)
        freeze_time
      end

      it "updates shop with new token information" do
        service.send(:refresh_token!)

        shop.reload
        expect(shop.lexoffice_token).to eq(access_token)
        expect(shop.lexoffice_refresh_token).to eq(refresh_token)
        expect(shop.lexoffice_token_expires_at).to eq((Time.zone.now + expires_in).to_i)
      end

      it "returns truthy value" do
        expect(service.send(:refresh_token!)).to be_truthy
      end
    end

    context "when RestClient::BadRequest is raised" do
      let(:error_response) { { error: "invalid_grant" }.to_json }
      let(:bad_request_error) do
        error = RestClient::BadRequest.new
        allow(error).to receive(:response).and_return(error_response)
        error
      end

      before do
        token_service_double = double("Lexoffice::TokenService")
        allow(Lexoffice::TokenService).to receive(:new).and_return(token_service_double)
        allow(token_service_double).to receive(:call).and_raise(bad_request_error)
        allow(Rails.error).to receive(:report)
        allow(shop).to receive(:mark_for_connection_reset)
        allow(shop).to receive(:send_mail_for_connection_reset)
      end

      it "reports the error to Rails.error with correct context" do
        service.send(:refresh_token!)

        expect(Rails.error).to have_received(:report).with(
          bad_request_error,
          context: {
            tags: "token_refresh_failed",
            user_id: shop.shopify_domain,
            response_body: error_response
          }
        )
      end

      context "when error is invalid_grant" do
        it "marks shop for connection reset" do
          service.send(:refresh_token!)
          expect(shop).to have_received(:mark_for_connection_reset)
        end

        it "sends mail for connection reset" do
          service.send(:refresh_token!)
          expect(shop).to have_received(:send_mail_for_connection_reset)
        end
      end

      context "when error is not invalid_grant" do
        let(:error_response) { { error: "other_error" }.to_json }

        it "does not mark shop for connection reset" do
          service.send(:refresh_token!)
          expect(shop).not_to have_received(:mark_for_connection_reset)
        end

        it "does not send mail for connection reset" do
          service.send(:refresh_token!)
          expect(shop).not_to have_received(:send_mail_for_connection_reset)
        end
      end

      context "when error response is not valid JSON" do
        let(:error_response) { "invalid json" }

        it "does not mark shop for connection reset" do
          service.send(:refresh_token!)
          expect(shop).not_to have_received(:mark_for_connection_reset)
        end

        it "does not send mail for connection reset" do
          service.send(:refresh_token!)
          expect(shop).not_to have_received(:send_mail_for_connection_reset)
        end

        it "still reports the error" do
          service.send(:refresh_token!)

          expect(Rails.error).to have_received(:report).with(
            bad_request_error,
            context: {
              tags: "token_refresh_failed",
              user_id: shop.shopify_domain,
              response_body: error_response
            }
          )
        end
      end

      context "when error response is missing error key" do
        let(:error_response) { { message: "some other error" }.to_json }

        it "does not mark shop for connection reset" do
          service.send(:refresh_token!)
          expect(shop).not_to have_received(:mark_for_connection_reset)
        end

        it "does not send mail for connection reset" do
          service.send(:refresh_token!)
          expect(shop).not_to have_received(:send_mail_for_connection_reset)
        end
      end
    end

    context "when successful response is not valid JSON" do
      before do
        token_service_double = double("Lexoffice::TokenService")
        allow(Lexoffice::TokenService).to receive(:new).and_return(token_service_double)
        allow(token_service_double).to receive(:call).and_return("invalid json")
      end

      it "raises JSON::ParserError" do
        expect { service.send(:refresh_token!) }.to raise_error(JSON::ParserError)
      end
    end
  end
end
