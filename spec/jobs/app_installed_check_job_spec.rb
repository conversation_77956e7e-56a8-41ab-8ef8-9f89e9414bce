# frozen_string_literal: true

# spec/jobs/shop_installed_check_job_spec.rb
require "rails_helper"

RSpec.describe AppInstalledCheckJob do
  let(:shop) { create(:shop) }
  let(:job_params) { { "shop_id" => shop.id } }

  subject(:run_job) { described_class.new.perform(job_params) }

  describe "#perform" do
    context "when app is already marked as uninstalled" do
      before { shop.update(uninstalled_at: Time.current) }

      it "does nothing" do
        expect(shop).not_to receive(:with_shopify_session)
        run_job
      end
    end

    context "when app is installed" do
      let(:api_response) { instance_double(ShopifyAPI::Shop, id: shop.shopify_domain) }

      before { allow(shop).to receive(:with_shopify_session).and_return(api_response) }

      it "makes API call and keeps shop as installed" do
        expect { run_job }.not_to(change { shop.reload.uninstalled_at })
      end
    end

    context "with errors" do
      let(:error) { ShopifyAPI::Errors::HttpResponseError.new(response: error_response) }

      before do
        allow(shop).to receive(:with_shopify_session).and_yield
        allow(ShopifyAPI::Shop).to receive(:current).and_raise(error)
      end

      context "when shop is not found on Shopify or app was deinstalled" do
        let(:error_response) { ShopifyAPI::Clients::HttpResponse.new(code: 404, headers: {}, body: "Not Found") }

        it "marks app as uninstalled" do
          expect { run_job }.to change { shop.reload.uninstalled_at }.from(nil).to(Time.zone.at(0))
        end
      end

      context "when other API error occurs" do
        let(:error_response) { ShopifyAPI::Clients::HttpResponse.new(code: 500, headers: {}, body: "") }

        it "raises the error" do
          expect { run_job }.to raise_error(ShopifyAPI::Errors::HttpResponseError)
        end

        it "does not mark app as uninstalled" do
          expect do
            run_job
          rescue ShopifyAPI::Errors::HttpResponseError
            # Catch error to check shop state
          end.not_to change { shop.reload.uninstalled_at }.from(nil)
        end
      end
    end
  end
end
